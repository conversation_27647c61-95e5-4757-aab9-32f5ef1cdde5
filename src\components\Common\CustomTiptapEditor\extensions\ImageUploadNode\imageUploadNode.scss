:root {
  --tt-button-default-icon-color: var(--tt-gray-light-a-600);

  --tiptap-image-upload-active: var(--tt-brand-color-500);
  --tiptap-image-upload-progress-bg: var(--tt-brand-color-50);
  --tiptap-image-upload-icon-bg: var(--tt-brand-color-500);

  --tiptap-image-upload-text-color: var(--tt-gray-light-a-700);
  --tiptap-image-upload-subtext-color: var(--tt-gray-light-a-400);
  --tiptap-image-upload-border: var(--tt-gray-light-a-300);
  --tiptap-image-upload-border-hover: var(--tt-gray-light-a-400);
  --tiptap-image-upload-border-active: var(--tt-brand-color-500);

  --tiptap-image-upload-icon-doc-bg: var(--tt-gray-light-a-200);
  --tiptap-image-upload-icon-doc-border: var(--tt-gray-light-300);
  --tiptap-image-upload-icon-color: var(--white);
}

.dark {
  --tt-button-default-icon-color: var(--tt-gray-dark-a-600);

  --tiptap-image-upload-active: var(--tt-brand-color-400);
  --tiptap-image-upload-progress-bg: var(--tt-brand-color-900);
  --tiptap-image-upload-icon-bg: var(--tt-brand-color-400);

  --tiptap-image-upload-text-color: var(--tt-gray-dark-a-700);
  --tiptap-image-upload-subtext-color: var(--tt-gray-dark-a-400);
  --tiptap-image-upload-border: var(--tt-gray-dark-a-300);
  --tiptap-image-upload-border-hover: var(--tt-gray-dark-a-400);
  --tiptap-image-upload-border-active: var(--tt-brand-color-400);

  --tiptap-image-upload-icon-doc-bg: var(--tt-gray-dark-a-200);
  --tiptap-image-upload-icon-doc-border: var(--tt-gray-dark-300);
  --tiptap-image-upload-icon-color: var(--black);
}

.tiptap-image-upload {
  margin: 2rem 0;

  input[type="file"] {
    display: none;
  }

  .tiptap-image-upload-dropzone {
    position: relative;
    width: 3.125rem;
    height: 3.75rem;
    display: inline-flex;
    align-items: flex-start;
    justify-content: center;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none;
  }

  .tiptap-image-upload-icon-container {
    position: absolute;
    width: 1.75rem;
    height: 1.75rem;
    bottom: 0;
    right: 0;
    background-color: var(--tiptap-image-upload-icon-bg);
    border-radius: var(--tt-radius-lg, 0.75rem);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .tiptap-image-upload-icon {
    width: 0.875rem;
    height: 0.875rem;
    color: var(--tiptap-image-upload-icon-color);
  }

  .tiptap-image-upload-dropzone-rect-primary {
    color: var(--tiptap-image-upload-icon-doc-bg);
    position: absolute;
  }

  .tiptap-image-upload-dropzone-rect-secondary {
    position: absolute;
    top: 0;
    right: 0.25rem;
    bottom: 0;
    color: var(--tiptap-image-upload-icon-doc-border);
  }

  .tiptap-image-upload-text {
    color: var(--tiptap-image-upload-text-color);
    font-weight: 500;
    font-size: 0.875rem;
    line-height: normal;

    em {
      font-style: normal;
      text-decoration: underline;
    }
  }

  .tiptap-image-upload-subtext {
    color: var(--tiptap-image-upload-subtext-color);
    font-weight: 600;
    line-height: normal;
    font-size: 0.75rem;
  }

  .tiptap-image-upload-preview {
    position: relative;
    border-radius: var(--tt-radius-md, 0.5rem);
    overflow: hidden;

    .tiptap-image-upload-progress {
      position: absolute;
      inset: 0;
      background-color: var(--tiptap-image-upload-progress-bg);
      transition: all 300ms ease-out;
    }

    .tiptap-image-upload-preview-content {
      position: relative;
      border: 1px solid var(--tiptap-image-upload-border);
      border-radius: var(--tt-radius-md, 0.5rem);
      padding: 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
    }

    .tiptap-image-upload-file-info {
      display: flex;
      align-items: center;
      gap: 0.75rem;
      height: 2rem;

      .tiptap-image-upload-file-icon {
        padding: 0.5rem;
        background-color: var(--tiptap-image-upload-icon-bg);
        border-radius: var(--tt-radius-lg, 0.75rem);

        svg {
          width: 0.875rem;
          height: 0.875rem;
          color: var(--tiptap-image-upload-icon-color);
        }
      }
    }

    .tiptap-image-upload-details {
      display: flex;
      flex-direction: column;
    }

    .tiptap-image-upload-actions {
      display: flex;
      align-items: center;

      .tiptap-image-upload-progress-text {
        font-size: 0.75rem;
        color: var(--tiptap-image-upload-border-active);
      }

      .tiptap-image-upload-close-btn {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2rem;
        height: 2rem;
        color: var(--tt-button-default-icon-color);
        transition: color 200ms ease;

        svg {
          width: 1rem;
          height: 1rem;
        }
      }
    }
  }

  .tiptap-image-upload-dragger {
    padding: 2rem 1.5rem;
    border: 1.5px dashed var(--tiptap-image-upload-border);
    border-radius: var(--tt-radius-md, 0.5rem);
    text-align: center;
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &-active {
      border-color: var(--tiptap-image-upload-border-active);
      background-color: rgba(
        var(--tiptap-image-upload-active-rgb, 0, 0, 255),
        0.05
      );
    }
  }

  .tiptap-image-upload-content {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.25rem;
    -webkit-user-select: none; /* Safari */
    -ms-user-select: none; /* IE 10 and IE 11 */
    user-select: none;
  }
}

.tiptap.ProseMirror.ProseMirror-focused {
  .ProseMirror-selectednode .tiptap-image-upload-dragger {
    border-color: var(--tiptap-image-upload-active);
  }
}

// New Image Upload Node Styles
.image-upload-node {
  margin: 1rem 0;
  border-radius: 8px;
  overflow: hidden;

  input[type="file"] {
    display: none;
  }

  .upload-drag-area {
    padding: 2rem 1.5rem;
    border: 2px dashed #e2e8f0;
    border-radius: 8px;
    text-align: center;
    cursor: pointer;
    transition: all 0.2s ease;
    background-color: transparent;

    &:hover {
      border-color: #cbd5e1;
    }

    &.upload-drag-area-active {
      border-color: #6366f1;
      background-color: rgba(99, 102, 241, 0.05);
    }
  }

  .upload-dropzone {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    user-select: none;
  }

  .upload-icon-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;

    .document-icon {
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
    }

    .upload-icon-overlay {
      position: absolute;
      bottom: -8px;
      right: -8px;

      .upload-icon {
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }
    }
  }

  .upload-content {
    text-align: center;

    .upload-text {
      display: block;
      color: #374151;
      font-size: 0.875rem;
      margin-bottom: 0.25rem;

      strong {
        color: #6366f1;
        font-weight: 600;
      }
    }

    .upload-subtext {
      display: block;
      color: #6b7280;
      font-size: 0.75rem;
    }
  }

  .upload-preview {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    overflow: hidden;
    position: relative;

    .upload-preview-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0.75rem 1rem;
      position: relative;
      z-index: 2;

      .upload-file-info {
        display: flex;
        align-items: center;
        gap: 0.75rem;
        flex: 1;
        min-width: 0;

        .upload-file-icon {
          flex-shrink: 0;

          .upload-icon {
            width: 1.5rem;
            height: 1.5rem;
          }
        }

        .upload-file-details {
          flex: 1;
          min-width: 0;

          .upload-file-name {
            color: #374151;
            font-size: 0.875rem;
            font-weight: 500;
            margin-bottom: 0.125rem;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .upload-file-size {
            color: #6b7280;
            font-size: 0.75rem;
          }
        }
      }

      .upload-actions {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        flex-shrink: 0;

        .upload-progress-text {
          color: #6366f1;
          font-size: 0.75rem;
          font-weight: 600;
          min-width: 2rem;
          text-align: right;
        }

        .upload-close-btn {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 1.5rem;
          height: 1.5rem;
          border: none;
          background: none;
          color: #6b7280;
          border-radius: 0.25rem;
          cursor: pointer;
          transition: all 0.2s ease;

          &:hover {
            background: #e2e8f0;
            color: #374151;
          }
        }
      }
    }

    .upload-progress-bar {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 3px;
      background: #e2e8f0;
      z-index: 1;

      .upload-progress-fill {
        height: 100%;
        background: #6366f1;
        transition: width 0.3s ease;
        border-radius: 0 3px 3px 0;
      }
    }
  }
}
