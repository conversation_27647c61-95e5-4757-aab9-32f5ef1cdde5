import { useState } from "react";
import {
  RiArrowLeftSLine,
  RiArrowRightSLine,
  RiCloseLine,
  RiSparkling2Line,
} from "@remixicon/react";
import { DefaultProfile } from "components/Common";
import { Image, Offcanvas, Button } from "react-bootstrap";
import { Link, useNavigate } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import useUserStore from "stores/user";
import { generateNickName } from "utils";
import HistoryBox from "../HistoryBox";
import "./styles.scss";
import { SETTINGS } from "globals";

interface HistoryOffcanvasProps {
  show: boolean;
  handleClose: () => void;
}

const HistoryOffcanvas: React.FC<HistoryOffcanvasProps> = ({
  show,
  handleClose,
}) => {
  const navigate = useNavigate();
  const user = useUserStore((state) => state.userInfo.user);
  const defaultView = "menu";
  const [activeView, setActiveView] = useState<"menu" | "history" | "report">(
    defaultView
  );

  const handleBackToMenu = () => setActiveView("menu");

  const handleOpenView = (view: "history" | "report" | "contact") => {
    if (view === "contact") {
      handleClose();
      navigate(ROUTE_PATH.CONTACT_US);
    } else {
      setActiveView(view);
    }
  };

  const renderMainMenu = () => (
    <div className="p-3 d-flex flex-column gap-3">
      <button
        className="mobile-navigation-btn d-flex justify-content-between align-items-center"
        onClick={() => handleOpenView("history")}
      >
        Chat History
        <RiArrowRightSLine />
      </button>
      <button
        className="mobile-navigation-btn d-flex justify-content-between align-items-center"
        onClick={() => handleOpenView("report")}
      >
        Report
        <RiArrowRightSLine />
      </button>
      <button
        className="mobile-navigation-btn d-flex justify-content-between align-items-center"
        onClick={() => handleOpenView("contact")}
      >
        Contact Us
        <RiArrowRightSLine />
      </button>
    </div>
  );

  const renderView = () => {
    switch (activeView) {
      case "history":
        return (
          <div className="">
            <HistoryBox onCloseCanvas={handleClose} />
          </div>
        );
      case "report":
        return (
          <div className="p-2">
            <button className="back-btn" onClick={handleBackToMenu}>
              ← Back
            </button>
            <HistoryBox onCloseCanvas={handleClose} type="report" />{" "}
          </div>
        );
      case "menu":
      default:
        return renderMainMenu();
    }
  };

  return (
    <Offcanvas
      show={show}
      onHide={handleClose}
      className="chat-history-offcanvas"
    >
      <Offcanvas.Header className="chat-history-offcanvas-header flex-wrap pb-0">
        <Offcanvas.Title className="d-flex justify-content-between w-100">
          <p className="mb-0 page-title fw-bold">{SETTINGS.APP_NAME}</p>
          <div className="d-flex gap-2">
            {activeView !== "menu" && (
              <Button
                variant=""
                className="bg-blue px-1 py-1"
                onClick={handleBackToMenu}
              >
                <RiArrowLeftSLine color="white" />
              </Button>
            )}
            <Button
              variant=""
              className="bg-blue px-1 py-1"
              onClick={handleClose}
            >
              <RiCloseLine color="white" />
            </Button>
          </div>
        </Offcanvas.Title>
        <hr className="mt-0 d-block w-100 order-3" />
      </Offcanvas.Header>

      <Offcanvas.Body className="chat-history-offcanvas-body p-0 d-flex flex-column">
        <div className="flex-grow-1 overflow-auto">{renderView()}</div>

        <div className="history-footer position-sticky bottom-0 d-flex flex-column bg-light">
          <Link
            to={ROUTE_PATH.SUBSCRIPTIONS}
            onClick={handleClose}
            className="history-footer-profile d-flex font-primary text-decoration-none"
            style={{ gap: "10px" }}
          >
            <RiSparkling2Line
              color="#0d3149"
              size={"35px"}
              className="rounded-circle"
            />
            <div>
              <h6 className="fw-bold mb-1">Upgrade plan</h6>
              <small className="mb-0">upgrade your plan today</small>
            </div>
          </Link>

          <Link
            to={ROUTE_PATH.SETTINGS}
            onClick={handleClose}
            className="history-footer-plans d-flex font-primary text-decoration-none fw-bold align-items-center text-truncate"
          >
            {user?.profile_photo ? (
              <Image
                src={user?.profile_photo}
                className="object-fit-cover profile-data-img rounded-circle bg-brown"
                alt="user"
              />
            ) : (
              <DefaultProfile
                text={generateNickName(user?.full_name)}
                className="small"
              />
            )}
            <span className="text-truncate">{user?.full_name}</span>
          </Link>
        </div>
      </Offcanvas.Body>
    </Offcanvas>
  );
};

export default HistoryOffcanvas;
