import {
  RiDeleteBin5Fill,
  RiInboxArchiveFill,
  RiInboxUnarchiveFill,
  RiMoreFill,
} from "@remixicon/react";
import React, { useEffect, useRef, useState } from "react";
import { Button } from "react-bootstrap";
import { isMobileOnly } from "react-device-detect";
import useArchiveReport from "./useArchiveReport";
import useDeleteReport from "./useDeleteReport";

const HistoryItemAction = ({
  itemInfo,
  openItemId,
  setOpenItemId,
  setHistoryList,
  setTotalCount,
}: any) => {
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);
  const [openUpwards, setOpenUpwards] = useState(false);

  const { onClickDelete } = useDeleteReport({
    itemInfo,
    setHistoryList,
    setTotalCount,
  });
  const { onClickArchive } = useArchiveReport({
    itemInfo,
    setHistoryList,
  });

  const toggleDropdown = (e: React.MouseEvent, chat_id: string) => {
    e.stopPropagation();
    setOpenItemId(openItemId === chat_id ? null : chat_id);
  };

  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setOpenItemId(null);
      }
    };
    document.addEventListener("mousedown", handleOutsideClick);
    return () => {
      document.removeEventListener("mousedown", handleOutsideClick);
    };
  }, [setOpenItemId]);

  useEffect(() => {
    if (openItemId === itemInfo.id && buttonRef.current && menuRef.current) {
      const buttonRect = buttonRef.current.getBoundingClientRect();
      const menuHeight = menuRef.current.offsetHeight;
      const viewportHeight = window.innerHeight;

      setOpenUpwards(
        buttonRect.bottom + menuHeight + (isMobileOnly ? 130 : 60) >
          viewportHeight
      );
    }
  }, [openItemId, itemInfo.id]);

  return (
    <>
      <Button
        ref={buttonRef}
        variant="link"
        className="list-item-wrapper-delete d-block position-absolute end-0 translate-middle-y top-50 bottom-0 h-100 m-0 text-center p-0"
        onClick={(e) => toggleDropdown(e, itemInfo.id)}
      >
        <RiMoreFill size={"20px"} color="#7d7d7d" />
      </Button>

      {openItemId === itemInfo.id && (
        <div
          ref={menuRef}
          className={`list-item-wrapper-delete-list position-absolute flex-column d-flex ${
            openUpwards
              ? "dropup dropdown-animate-up"
              : "dropdown dropdown-animate-down"
          }`}
          style={{
            top: openUpwards ? "auto" : "100%",
            bottom: openUpwards ? "100%" : "auto",
          }}
        >
          <Button
            variant="link"
            className="mb-0 lh-1 font-primary fw-medium border-0 text-decoration-none text-start"
            onClick={onClickArchive}
          >
            {itemInfo?.is_archived ? (
              <>
                <RiInboxUnarchiveFill size={"18px"} className="me-2" />
                Unarchive
              </>
            ) : (
              <>
                <RiInboxArchiveFill size={"18px"} className="me-2" />
                Archive
              </>
            )}
          </Button>

          <Button
            variant="link"
            className="mb-0 lh-1 font-primary fw-medium border-0 text-decoration-none text-start"
            onClick={onClickDelete}
          >
            <RiDeleteBin5Fill size={"18px"} className="me-2" />
            Delete
          </Button>
        </div>
      )}
    </>
  );
};

export default HistoryItemAction;
