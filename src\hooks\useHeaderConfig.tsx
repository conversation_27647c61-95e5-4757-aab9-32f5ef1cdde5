import { REPORTS_ROUTE_PATH } from "features/Reports/routePath";
import { useLocation } from "react-router-dom";
import { ROUTE_PATH } from "routes";
import { UserInterface } from "types";

interface IHeaderConfig {
  user: UserInterface;
}

const useHeaderConfig = ({ user }: IHeaderConfig) => {
  const location = useLocation();
  const pathname = location.pathname;

  if (pathname.includes("reports")) {
    return [
      {
        path: REPORTS_ROUTE_PATH.REPORTS,
        label: "Build",
      },
      {
        path: REPORTS_ROUTE_PATH.GENERATE_REPORT,
        label: "Generate",
      },
    ];
  }

  return [
    {
      path: user?.is_subscription ? ROUTE_PATH.HOME : ROUTE_PATH.SUBSCRIPTIONS,
      label: "Home",
      isDefault: true,
    },
  ];
};

export default useHeaderConfig;
