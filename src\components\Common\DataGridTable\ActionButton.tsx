import { Button } from "react-bootstrap";
import HoverTooltip from "../HoverTooltip";

const ActionButton = ({ title, onClick, disabled, icon, customClass }: any) => {
  return (
    <HoverTooltip title={title} customClass="fw-bold">
      <Button
        className={`bg-blue border-blue p-2 lh-1 action-item-btn ${customClass}`}
        onClick={onClick}
        disabled={disabled}
      >
        {icon}
      </Button>
    </HoverTooltip>
  );
};

export default ActionButton;
