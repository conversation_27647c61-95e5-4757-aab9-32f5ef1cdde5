import { useUploadReportAsset as useUploadReportAssetMutation } from "features/Reports/api";
import { useCallback, useState } from "react";
import toast from "react-hot-toast";

export const MAX_FILE_SIZE = 20 * 1024 * 1024;

export interface UploadProgress {
  progress: number;
}

export interface UploadReportAssetOptions {
  onProgress?: (event: UploadProgress) => void;
  onSuccess?: (response: { assetId: string; url: string }) => void;
  onError?: (error: Error) => void;
}

export interface UploadResponse {
  url: string;
  assetId: string;
}

export interface UploadReportAssetResult {
  uploadAsset: (
    file: File,
    abortSignal?: AbortSignal
  ) => Promise<string | UploadResponse>;
  isUploading: boolean;
  progress: number;
  error: Error | null;
  reset: () => void;
}

export const useUploadReportAsset = (
  options: UploadReportAssetOptions = {}
): UploadReportAssetResult => {
  const [isUploading, setIsUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<Error | null>(null);

  const { mutateAsync: uploadReportAssetMutation } =
    useUploadReportAssetMutation();

  const validateFile = (file: File): void => {
    // Validate file type
    if (!file.type.startsWith("image/")) {
      const error = new Error("Invalid file type");
      toast.error("Please upload a valid image file");
      throw error;
    }

    // Validate file size
    if (file.size === 0) {
      const error = new Error("Invalid empty file");
      toast.error("Invalid empty file!");
      throw error;
    }

    if (file.size > MAX_FILE_SIZE) {
      const error = new Error("File too large");
      toast.error("Image size should be less than 20MB");
      throw error;
    }
  };

  const simulateProgress = (
    onProgress: (event: UploadProgress) => void,
    abortSignal?: AbortSignal
  ): (() => void) => {
    let progress = 0;
    onProgress({ progress: 0 });

    const interval = setInterval(() => {
      if (abortSignal?.aborted) {
        clearInterval(interval);
        return;
      }

      progress += 5; // 5% increments
      if (progress <= 90) {
        onProgress({ progress });
      }
    }, 100); // Every 100ms

    return () => clearInterval(interval);
  };

  const uploadAsset = useCallback(
    async (
      file: File,
      abortSignal?: AbortSignal
    ): Promise<string | UploadResponse> => {
      console.log("useUploadReportAsset called with:", {
        fileName: file.name,
        fileSize: file.size,
        hasProgress: !!options.onProgress,
      });

      try {
        setIsUploading(true);
        setProgress(0);
        setError(null);

        // Validate the file
        validateFile(file);

        // Check if operation was aborted
        if (abortSignal?.aborted) {
          throw new Error("Upload aborted");
        }

        // Start progress simulation
        const stopProgress = simulateProgress((event) => {
          setProgress(event.progress);
          options.onProgress?.(event);
        }, abortSignal);

        // Create FormData payload
        const formData = new FormData();
        formData.append("assetType", "image");
        formData.append("file", file);

        try {
          // Call the API
          const response = await uploadReportAssetMutation(formData);

          // Stop progress simulation
          stopProgress();

          // Check if operation was aborted
          if (abortSignal?.aborted) {
            throw new Error("Upload aborted");
          }

          // Complete progress
          setProgress(100);
          options.onProgress?.({ progress: 100 });

          // Extract response data
          const responseData = response?.data?.asset;
          if (!responseData?.url) {
            throw new Error("Upload failed: No URL returned");
          }

          // Call success callback
          options.onSuccess?.(responseData);

          // Return the full response object if it has assetId, otherwise just the URL
          if (responseData.asset_id) {
            return {
              url: responseData.url,
              assetId: responseData.asset_id,
            };
          }

          return responseData.url;
        } catch (apiError: any) {
          stopProgress();
          throw apiError;
        }
      } catch (err: any) {
        const error = err instanceof Error ? err : new Error("Upload failed");
        setError(error);
        options.onError?.(error);
        throw error;
      } finally {
        setIsUploading(false);
      }
    },
    [uploadReportAssetMutation, options]
  );

  const reset = useCallback(() => {
    setIsUploading(false);
    setProgress(0);
    setError(null);
  }, []);

  return {
    uploadAsset,
    isUploading,
    progress,
    error,
    reset,
  };
};

// Factory function to create upload handler for TipTap ImageUploadNode
export const createImageUploadHandler = (
  uploadAsset: (
    file: File,
    abortSignal?: AbortSignal
  ) => Promise<string | UploadResponse>
) => {
  return async (
    file: File,
    onProgress?: (event: { progress: number }) => void,
    abortSignal?: AbortSignal
  ): Promise<string | UploadResponse> => {
    return uploadAsset(file, abortSignal);
  };
};
