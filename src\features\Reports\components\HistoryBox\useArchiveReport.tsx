import { RiInboxArchiveFill, RiInboxUnarchiveFill } from "@remixicon/react";
import { useArchiveReportMutation } from "features/Reports/api";
import toast from "react-hot-toast";
import { setConfirmModalConfig } from "stores";

const useArchiveReport = ({ itemInfo, setHistoryList }: any) => {
  const { mutateAsync: archiveHistory } = useArchiveReportMutation();

  const handleArchive = async (id: string) => {
    try {
      const result: any = await archiveHistory(id);
      if (result?.success) {
        toast.success(result?.message || "Report Archived Successfully!");
        setHistoryList((prev: any) =>
          prev.map((item: any) => {
            if (item.id === id) {
              return {
                ...item,
                is_archived: !item?.is_archived,
              };
            }
            return item;
          })
        );
      }
    } catch (err) {
      console.error(err);
    }
  };

  const onClickArchive = (evt: React.MouseEvent) => {
    evt.stopPropagation();
    setConfirmModalConfig({
      visible: true,
      data: {
        onSubmit: () => handleArchive(itemInfo.id),
        content: {
          heading: itemInfo?.is_archived ? "Unarchive" : "Archive",
          description: `Are you sure you want to ${
            itemInfo?.is_archived ? "unarchive" : "archive"
          } this report?`,
        },
        icon: itemInfo?.is_archived ? RiInboxUnarchiveFill : RiInboxArchiveFill,
        iconColor: "#ad986f",
      },
    });
  };

  return { onClickArchive };
};

export default useArchiveReport;
